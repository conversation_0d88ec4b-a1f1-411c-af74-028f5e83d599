.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e0e0e0;
}

header h1 {
  color: #333;
  margin-bottom: 0.5rem;
}

.server-status {
  font-size: 0.9rem;
  margin: 0;
}

.status-ok {
  color: #4caf50;
  font-weight: bold;
}

.status-error {
  color: #f44336;
  font-weight: bold;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border-left: 4px solid #f44336;
}

.create-item {
  background-color: #f5f5f5;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.create-item h2 {
  margin-top: 0;
  color: #333;
}

.create-item form {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.create-item input {
  flex: 1;
  min-width: 200px;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.create-item button {
  padding: 0.75rem 1.5rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.create-item button:hover:not(:disabled) {
  background-color: #1976d2;
}

.create-item button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.items-list h2 {
  color: #333;
  margin-bottom: 1rem;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.item-card {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
}

.item-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.item-card h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
}

.item-card p {
  color: #666;
  margin-bottom: 1rem;
}

.item-actions {
  display: flex;
  gap: 0.5rem;
}

.item-actions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.item-actions button:first-child {
  background-color: #ff9800;
  color: white;
}

.item-actions button:first-child:hover:not(:disabled) {
  background-color: #f57c00;
}

.item-actions button:last-child {
  background-color: #f44336;
  color: white;
}

.item-actions button:last-child:hover:not(:disabled) {
  background-color: #d32f2f;
}

.item-actions button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.item-card form {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.item-card form input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .create-item form {
    flex-direction: column;
  }

  .create-item input {
    min-width: auto;
  }

  .items-grid {
    grid-template-columns: 1fr;
  }
}
