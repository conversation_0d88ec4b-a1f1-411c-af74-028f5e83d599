/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: white;
}

.App {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Navigation Bar */
.navbar {
  background-color: white;
  box-shadow: 0 2px 10px rgba(78, 184, 240, 0.1);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo h2 {
  color: #4EB8F0;
  font-size: 1.8rem;
  font-weight: bold;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #4EB8F0;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #4EB8F0 0%, #66C6F8 50%, #BFE9FF 100%);
  color: white;
  padding: 150px 0 100px;
  text-align: center;
  margin-top: 70px;
}

.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-content p {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.95;
}

.cta-button {
  background-color: white;
  color: #4EB8F0;
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Products Section */
.products {
  padding: 100px 0;
  background-color: #BFE9FF;
}

.products h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.product-card {
  background-color: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(78, 184, 240, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(78, 184, 240, 0.2);
}

.product-image {
  margin-bottom: 1.5rem;
}

.placeholder-image {
  background: linear-gradient(135deg, #66C6F8, #4EB8F0);
  color: white;
  padding: 3rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1.1rem;
}

.product-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.product-card p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.price {
  font-size: 2rem;
  font-weight: bold;
  color: #4EB8F0;
  margin-bottom: 1.5rem;
}

.product-btn {
  background-color: #4EB8F0;
  color: white;
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.product-btn:hover {
  background-color: #66C6F8;
}

/* Features Section */
.features {
  padding: 100px 0;
  background-color: white;
}

.features h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  text-align: center;
  padding: 2rem;
  border-radius: 15px;
  background-color: #BFE9FF;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* About Section */
.about {
  padding: 100px 0;
  background-color: #66C6F8;
  color: white;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  opacity: 0.95;
}

.about-image .placeholder-image {
  background: linear-gradient(135deg, #4EB8F0, white);
  color: #333;
  padding: 4rem 2rem;
  border-radius: 15px;
  font-weight: 600;
  font-size: 1.2rem;
  text-align: center;
}

/* Contact Section */
.contact {
  padding: 100px 0;
  background-color: white;
  text-align: center;
}

.contact h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.contact > p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.contact-item {
  padding: 2rem;
  background-color: #BFE9FF;
  border-radius: 15px;
}

.contact-item h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
}

.contact-item p {
  color: #666;
  line-height: 1.6;
}

/* Footer */
.footer {
  background-color: #4EB8F0;
  color: white;
  padding: 2rem 0;
  text-align: center;
}

.footer p {
  margin: 0;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-content p {
    font-size: 1.1rem;
  }

  .products-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-info {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 1rem;
  }
}
