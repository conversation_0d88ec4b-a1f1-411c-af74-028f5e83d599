version: '3.8'

services:
  # Backend service (FastAPI)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: luxefridge-backend
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - CORS_ORIGINS=http://localhost:3000,http://localhost:80,http://frontend
    ports:
      - "8000:8000"
    networks:
      - luxefridge-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend service (React + Nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: luxefridge-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - luxefridge-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  luxefridge-network:
    driver: bridge
    name: luxefridge-network

volumes:
  # Add volumes if needed for persistent data
  backend-data:
    name: luxefridge-backend-data
