<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Web Application Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f5f5f5;
        }
        
        header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .status {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 4px;
        }
        
        .status.ok {
            background-color: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }
        
        .status.error {
            background-color: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }
        
        .form-section {
            background-color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-section h2 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        
        button {
            padding: 0.75rem 1.5rem;
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 0.5rem;
        }
        
        button:hover:not(:disabled) {
            background-color: #1976d2;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .item-card {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .item-card h3 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            color: #333;
        }
        
        .item-card p {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .item-actions button {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .edit-btn {
            background-color: #ff9800 !important;
        }
        
        .edit-btn:hover:not(:disabled) {
            background-color: #f57c00 !important;
        }
        
        .delete-btn {
            background-color: #f44336 !important;
        }
        
        .delete-btn:hover:not(:disabled) {
            background-color: #d32f2f !important;
        }
    </style>
</head>
<body>
    <header>
        <h1>My Web Application</h1>
        <div id="server-status" class="status">Checking server status...</div>
    </header>

    <main>
        <section class="form-section">
            <h2>Add New Item</h2>
            <form id="item-form">
                <div class="form-group">
                    <label for="item-name">Item Name:</label>
                    <input type="text" id="item-name" required>
                </div>
                <div class="form-group">
                    <label for="item-description">Description (optional):</label>
                    <input type="text" id="item-description">
                </div>
                <button type="submit">Add Item</button>
            </form>
        </section>

        <section class="form-section">
            <h2 id="items-title">Items (0)</h2>
            <button onclick="loadItems()">Refresh Items</button>
            <div id="items-container" class="items-grid"></div>
        </section>
    </main>

    <script>
        const API_BASE = 'http://localhost:8000';
        let items = [];

        // Check server status
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                document.getElementById('server-status').innerHTML = `Server Status: ${data.message}`;
                document.getElementById('server-status').className = 'status ok';
            } catch (error) {
                document.getElementById('server-status').innerHTML = 'Server Status: Backend server is not running';
                document.getElementById('server-status').className = 'status error';
            }
        }

        // Load items
        async function loadItems() {
            try {
                const response = await fetch(`${API_BASE}/api/items`);
                items = await response.json();
                renderItems();
            } catch (error) {
                console.error('Error loading items:', error);
                document.getElementById('items-container').innerHTML = '<p style="color: red;">Error loading items. Make sure the backend server is running.</p>';
            }
        }

        // Render items
        function renderItems() {
            const container = document.getElementById('items-container');
            const title = document.getElementById('items-title');
            
            title.textContent = `Items (${items.length})`;
            
            if (items.length === 0) {
                container.innerHTML = '<p>No items yet. Add one above!</p>';
                return;
            }

            container.innerHTML = items.map(item => `
                <div class="item-card">
                    <h3>${item.name}</h3>
                    ${item.description ? `<p>${item.description}</p>` : ''}
                    <div class="item-actions">
                        <button class="edit-btn" onclick="editItem(${item.id})">Edit</button>
                        <button class="delete-btn" onclick="deleteItem(${item.id})">Delete</button>
                    </div>
                </div>
            `).join('');
        }

        // Add item
        document.getElementById('item-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('item-name').value;
            const description = document.getElementById('item-description').value;
            
            try {
                const response = await fetch(`${API_BASE}/api/items`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name, description }),
                });
                
                if (response.ok) {
                    document.getElementById('item-name').value = '';
                    document.getElementById('item-description').value = '';
                    loadItems();
                } else {
                    alert('Error creating item');
                }
            } catch (error) {
                console.error('Error creating item:', error);
                alert('Error creating item. Make sure the backend server is running.');
            }
        });

        // Delete item
        async function deleteItem(id) {
            if (!confirm('Are you sure you want to delete this item?')) return;
            
            try {
                const response = await fetch(`${API_BASE}/api/items/${id}`, {
                    method: 'DELETE',
                });
                
                if (response.ok) {
                    loadItems();
                } else {
                    alert('Error deleting item');
                }
            } catch (error) {
                console.error('Error deleting item:', error);
                alert('Error deleting item');
            }
        }

        // Edit item (simplified - just prompt for new values)
        async function editItem(id) {
            const item = items.find(i => i.id === id);
            if (!item) return;
            
            const newName = prompt('Enter new name:', item.name);
            if (newName === null) return;
            
            const newDescription = prompt('Enter new description:', item.description || '');
            if (newDescription === null) return;
            
            try {
                const response = await fetch(`${API_BASE}/api/items/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name: newName, description: newDescription }),
                });
                
                if (response.ok) {
                    loadItems();
                } else {
                    alert('Error updating item');
                }
            } catch (error) {
                console.error('Error updating item:', error);
                alert('Error updating item');
            }
        }

        // Initialize
        checkServerStatus();
        loadItems();
    </script>
</body>
</html>
