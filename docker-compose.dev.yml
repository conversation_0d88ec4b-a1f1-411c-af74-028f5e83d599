version: '3.8'

services:
  # Backend service (FastAPI) - Development mode
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: luxefridge-backend-dev
    restart: unless-stopped
    environment:
      - ENVIRONMENT=development
      - CORS_ORIGINS=http://localhost:5173,http://localhost:3000
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend-cache:/app/__pycache__
    networks:
      - luxefridge-network
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # Frontend service (React) - Development mode with Vite
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: luxefridge-frontend-dev
    restart: unless-stopped
    environment:
      - VITE_API_URL=http://localhost:8000
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - frontend-node-modules:/app/node_modules
    networks:
      - luxefridge-network
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
    depends_on:
      - backend

networks:
  luxefridge-network:
    driver: bridge
    name: luxefridge-network-dev

volumes:
  backend-cache:
    name: luxefridge-backend-cache
  frontend-node-modules:
    name: luxefridge-frontend-node-modules
