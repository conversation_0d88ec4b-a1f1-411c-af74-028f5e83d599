# LuxeFridge - Luxury Refrigerator Sales Website

A full-stack web application built with React (TypeScript) frontend and FastAPI backend, containerized with Docker for easy deployment.

## 🏗️ Architecture

- **Frontend**: React + TypeScript + Vite (served with <PERSON>in<PERSON> in production)
- **Backend**: FastAPI + Python
- **Containerization**: Docker + Docker Compose
- **Networking**: Internal Docker network for service communication

## 🚀 Quick Start

### Prerequisites
- Docker
- Docker Compose

### Production Deployment

1. **Clone and navigate to the project:**
   ```bash
   cd websiteproject
   ```

2. **Build and run the application:**
   ```bash
   docker-compose up --build
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Development Mode

For development with hot reload:

```bash
docker-compose -f docker-compose.dev.yml up --build
```

- Frontend (Vite dev server): http://localhost:5173
- Backend (with auto-reload): http://localhost:8000

## 🐳 Docker Commands

### Production
```bash
# Build and start services
docker-compose up --build

# Run in background
docker-compose up -d --build

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Rebuild specific service
docker-compose build frontend
docker-compose build backend
```

### Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up --build

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

## 📁 Project Structure

```
websiteproject/
├── backend/
│   ├── Dockerfile              # Production backend container
│   ├── Dockerfile.dev          # Development backend container
│   ├── main.py                 # FastAPI application
│   ├── requirements.txt        # Python dependencies
│   └── .dockerignore
├── frontend/
│   ├── Dockerfile              # Production frontend container
│   ├── Dockerfile.dev          # Development frontend container
│   ├── nginx.conf              # Nginx configuration
│   ├── src/                    # React source code
│   ├── package.json            # Node.js dependencies
│   └── .dockerignore
├── docker-compose.yml          # Production orchestration
├── docker-compose.dev.yml      # Development orchestration
└── README.md
```

## 🔧 Configuration

### Environment Variables

The application uses environment variables for configuration:

- `ENVIRONMENT`: Set to "production" or "development"
- `CORS_ORIGINS`: Allowed CORS origins for the backend
- `VITE_API_URL`: API URL for the frontend (development only)

### Networking

Services communicate through a custom Docker network:
- Network name: `luxefridge-network`
- Frontend can reach backend at `http://backend:8000`
- External access via mapped ports

## 🏥 Health Checks

Both services include health checks:
- **Backend**: `GET /health` endpoint
- **Frontend**: HTTP check on port 80

## 🔄 Development Workflow

1. **Make changes** to your code
2. **Development mode** automatically reloads changes
3. **Production testing**: Rebuild containers to test production build

## 🛠️ Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 5173, and 8000 are available
2. **Build failures**: Check Docker logs with `docker-compose logs`
3. **Network issues**: Restart Docker daemon if services can't communicate

### Useful Commands

```bash
# Check running containers
docker ps

# View container logs
docker logs luxefridge-frontend
docker logs luxefridge-backend

# Execute commands in running container
docker exec -it luxefridge-backend bash
docker exec -it luxefridge-frontend sh

# Clean up Docker resources
docker system prune -a
```

## 📝 API Endpoints

- `GET /` - Welcome message
- `GET /health` - Health check
- `GET /api/items` - Get all items
- `POST /api/items` - Create new item
- `PUT /api/items/{id}` - Update item
- `DELETE /api/items/{id}` - Delete item

## 🎨 Frontend Features

- Luxury fridge sales website
- Responsive design
- Premium color scheme
- Product showcase
- Contact information
- Modern React with TypeScript
