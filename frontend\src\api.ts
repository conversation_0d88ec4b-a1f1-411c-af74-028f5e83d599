const API_BASE_URL = 'http://localhost:8000';

export interface Item {
  id?: number;
  name: string;
  description?: string;
}

export interface ItemResponse {
  id: number;
  name: string;
  description?: string;
}

class ApiService {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getItems(): Promise<ItemResponse[]> {
    return this.request<ItemResponse[]>('/api/items');
  }

  async createItem(item: Item): Promise<ItemResponse> {
    return this.request<ItemResponse>('/api/items', {
      method: 'POST',
      body: JSON.stringify(item),
    });
  }

  async updateItem(id: number, item: Item): Promise<ItemResponse> {
    return this.request<ItemResponse>(`/api/items/${id}`, {
      method: 'PUT',
      body: JSON.stringify(item),
    });
  }

  async deleteItem(id: number): Promise<{ message: string }> {
    return this.request<{ message: string }>(`/api/items/${id}`, {
      method: 'DELETE',
    });
  }

  async getServerStatus(): Promise<{ message: string }> {
    return this.request<{ message: string }>('/');
  }
}

export const apiService = new ApiService();
