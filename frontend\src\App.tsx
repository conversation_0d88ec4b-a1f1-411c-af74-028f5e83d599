import { useState, useEffect } from "react";
import { apiService, ItemResponse, Item } from "./api";
import "./App.css";

function App() {
  const [items, setItems] = useState<ItemResponse[]>([]);
  const [newItem, setNewItem] = useState<Item>({ name: "", description: "" });
  const [editingItem, setEditingItem] = useState<ItemResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [serverStatus, setServerStatus] = useState<string>("");

  useEffect(() => {
    loadItems();
    checkServerStatus();
  }, []);

  const checkServerStatus = async () => {
    try {
      const response = await apiService.getServerStatus();
      setServerStatus(response.message);
    } catch (err) {
      setServerStatus("Backend server is not running");
      setError(
        "Cannot connect to backend server. Make sure it's running on http://localhost:8000",
      );
    }
  };

  const loadItems = async () => {
    setLoading(true);
    setError(null);
    try {
      const fetchedItems = await apiService.getItems();
      setItems(fetchedItems);
    } catch (err) {
      setError("Failed to load items");
      console.error("Error loading items:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateItem = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newItem.name.trim()) return;

    setLoading(true);
    try {
      const createdItem = await apiService.createItem(newItem);
      setItems([...items, createdItem]);
      setNewItem({ name: "", description: "" });
    } catch (err) {
      setError("Failed to create item");
      console.error("Error creating item:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateItem = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingItem) return;

    setLoading(true);
    try {
      const updatedItem = await apiService.updateItem(editingItem.id, {
        name: editingItem.name,
        description: editingItem.description,
      });
      setItems(
        items.map((item) => (item.id === updatedItem.id ? updatedItem : item)),
      );
      setEditingItem(null);
    } catch (err) {
      setError("Failed to update item");
      console.error("Error updating item:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteItem = async (id: number) => {
    setLoading(true);
    try {
      await apiService.deleteItem(id);
      setItems(items.filter((item) => item.id !== id));
    } catch (err) {
      setError("Failed to delete item");
      console.error("Error deleting item:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="app">
      <header>
        <h1>My Web Application</h1>
        <p className="server-status">
          Server Status:{" "}
          <span
            className={
              serverStatus.includes("Hello") ? "status-ok" : "status-error"
            }
          >
            {serverStatus}
          </span>
        </p>
      </header>

      {error && <div className="error-message">{error}</div>}

      <main>
        <section className="create-item">
          <h2>Add New Item</h2>
          <form onSubmit={handleCreateItem}>
            <input
              type="text"
              placeholder="Item name"
              value={newItem.name}
              onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
              required
            />
            <input
              type="text"
              placeholder="Description (optional)"
              value={newItem.description}
              onChange={(e) =>
                setNewItem({ ...newItem, description: e.target.value })
              }
            />
            <button type="submit" disabled={loading}>
              {loading ? "Adding..." : "Add Item"}
            </button>
          </form>
        </section>

        <section className="items-list">
          <h2>Items ({items.length})</h2>
          {loading && <p>Loading...</p>}
          {items.length === 0 && !loading && (
            <p>No items yet. Add one above!</p>
          )}
          <div className="items-grid">
            {items.map((item) => (
              <div key={item.id} className="item-card">
                {editingItem?.id === item.id ? (
                  <form onSubmit={handleUpdateItem}>
                    <input
                      type="text"
                      value={editingItem.name}
                      onChange={(e) =>
                        setEditingItem({ ...editingItem, name: e.target.value })
                      }
                      required
                    />
                    <input
                      type="text"
                      value={editingItem.description || ""}
                      onChange={(e) =>
                        setEditingItem({
                          ...editingItem,
                          description: e.target.value,
                        })
                      }
                    />
                    <div className="item-actions">
                      <button type="submit" disabled={loading}>
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={() => setEditingItem(null)}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                ) : (
                  <>
                    <h3>{item.name}</h3>
                    {item.description && <p>{item.description}</p>}
                    <div className="item-actions">
                      <button onClick={() => setEditingItem(item)}>Edit</button>
                      <button
                        onClick={() => handleDeleteItem(item.id)}
                        disabled={loading}
                      >
                        Delete
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </section>
      </main>
    </div>
  );
}

export default App;
