import React from "react";
import "./App.css";

function App() {
  return (
    <div className="App">
      {/* Navigation Bar */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="nav-logo">
            <h2>LuxeFridge</h2>
          </div>
          <ul className="nav-menu">
            <li>
              <a href="#home">Home</a>
            </li>
            <li>
              <a href="#products">Products</a>
            </li>
            <li>
              <a href="#features">Features</a>
            </li>
            <li>
              <a href="#about">About</a>
            </li>
            <li>
              <a href="#contact">Contact</a>
            </li>
          </ul>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="hero">
        <div className="hero-content">
          <h1>The Ultimate in Luxury Refrigeration</h1>
          <p>
            Experience unparalleled elegance and performance with our premium
            collection of luxury refrigerators
          </p>
          <button className="cta-button">Explore Collection</button>
        </div>
      </section>

      {/* Products Section */}
      <section id="products" className="products">
        <div className="container">
          <h2>Our Premium Collection</h2>
          <div className="products-grid">
            <div className="product-card">
              <div className="product-image">
                <div className="placeholder-image">Premium Model X1</div>
              </div>
              <h3>LuxeFridge Elite X1</h3>
              <p>
                The pinnacle of refrigeration technology with smart features and
                elegant design
              </p>
              <div className="price">$12,999</div>
              <button className="product-btn">View Details</button>
            </div>
            <div className="product-card">
              <div className="product-image">
                <div className="placeholder-image">Premium Model X2</div>
              </div>
              <h3>LuxeFridge Elite X2</h3>
              <p>
                Spacious luxury with advanced cooling technology and premium
                finishes
              </p>
              <div className="price">$15,999</div>
              <button className="product-btn">View Details</button>
            </div>
            <div className="product-card">
              <div className="product-image">
                <div className="placeholder-image">Premium Model X3</div>
              </div>
              <h3>LuxeFridge Elite X3</h3>
              <p>
                The ultimate statement piece with cutting-edge features and
                bespoke design
              </p>
              <div className="price">$24,999</div>
              <button className="product-btn">View Details</button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features">
        <div className="container">
          <h2>Luxury Features</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">❄️</div>
              <h3>Advanced Cooling</h3>
              <p>
                Precision temperature control with multi-zone cooling technology
              </p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🏆</div>
              <h3>Premium Materials</h3>
              <p>Crafted with the finest stainless steel and luxury finishes</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">📱</div>
              <h3>Smart Technology</h3>
              <p>WiFi connectivity and app control for ultimate convenience</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🔧</div>
              <h3>Custom Design</h3>
              <p>Bespoke options to match your luxury kitchen perfectly</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="about">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2>Crafting Excellence Since 1985</h2>
              <p>
                For over three decades, LuxeFridge has been the premier choice
                for discerning customers who demand the finest in luxury
                refrigeration. Our commitment to excellence, innovation, and
                craftsmanship has made us the trusted name in high-end kitchen
                appliances.
              </p>
              <p>
                Each LuxeFridge is meticulously engineered and handcrafted to
                deliver not just superior performance, but an unmatched
                aesthetic that elevates your kitchen to new heights of
                sophistication.
              </p>
            </div>
            <div className="about-image">
              <div className="placeholder-image">Luxury Kitchen</div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="contact">
        <div className="container">
          <h2>Experience Luxury</h2>
          <p>
            Schedule a private consultation with our luxury appliance
            specialists
          </p>
          <div className="contact-info">
            <div className="contact-item">
              <h3>Showroom</h3>
              <p>
                123 Luxury Avenue
                <br />
                Beverly Hills, CA 90210
              </p>
            </div>
            <div className="contact-item">
              <h3>Phone</h3>
              <p>(555) 123-LUXE</p>
            </div>
            <div className="contact-item">
              <h3>Email</h3>
              <p><EMAIL></p>
            </div>
          </div>
          <button className="cta-button">Schedule Consultation</button>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <p>
            &copy; 2024 LuxeFridge. All rights reserved. | Privacy Policy |
            Terms of Service
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
